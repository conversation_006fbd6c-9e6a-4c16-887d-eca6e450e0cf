%% === Alternativní řešení pro stahování krypto dat ===
%% Používá CoinGecko API místo Money.Net

% Výstupní složky
baseDir = 'E:\02_Data\Crypto_alternative';
outDirParquet = fullfile(baseDir, 'parquet');
outDirMat = fullfile(baseDir, 'mat');

if ~exist(outDirParquet, 'dir'), mkdir(outDirParquet); end
if ~exist(outDirMat, 'dir'), mkdir(outDirMat); end

% Mapování symbolů na CoinGecko ID
cryptoMapping = containers.Map( ...
    {'BTC/USD', 'ETH/USD', 'BNB/USD', 'SOL/USD', 'XRP/USD', ...
     'DOGE/USD', 'ADA/USD', 'AVAX/USD', 'DOT/USD', 'TRX/USD', ...
     'LINK/USD', 'MATIC/USD', 'LTC/USD', 'BCH/USD', 'XLM/USD', ...
     'ATOM/USD', 'ETC/USD', 'HBAR/USD', 'NEAR/USD', 'ICP/USD'}, ...
    {'bitcoin', 'ethereum', 'binancecoin', 'solana', 'ripple', ...
     'dogecoin', 'cardano', 'avalanche-2', 'polkadot', 'tron', ...
     'chainlink', 'matic-network', 'litecoin', 'bitcoin-cash', 'stellar', ...
     'cosmos', 'ethereum-classic', 'hedera-hashgraph', 'near', 'internet-computer'} ...
);

fprintf('🚀 Spouštím alternativní stahování krypto dat z CoinGecko API...\n\n');

%% Test API připojení
fprintf('🔍 Testování CoinGecko API...\n');
try
    pingUrl = 'https://api.coingecko.com/api/v3/ping';
    response = webread(pingUrl, 'Timeout', 10);
    fprintf('✅ CoinGecko API je dostupné: %s\n\n', response.gecko_says);
catch
    error('❌ CoinGecko API není dostupné!');
end

%% Funkce pro stahování historických dat
function data = getCoinGeckoData(coinId, days)
    baseUrl = 'https://api.coingecko.com/api/v3/coins';
    url = sprintf('%s/%s/market_chart', baseUrl, coinId);
    
    try
        params = {'vs_currency', 'usd', 'days', num2str(days), 'interval', 'hourly'};
        response = webread(url, params{:}, 'Timeout', 30);
        
        % Konverze dat do tabulky
        prices = response.prices;
        volumes = response.total_volumes;
        
        timestamps = datetime(prices(:,1)/1000, 'ConvertFrom', 'posixtime');
        priceValues = prices(:,2);
        volumeValues = volumes(:,2);
        
        data = table(timestamps, priceValues, volumeValues, ...
                    'VariableNames', {'Timestamp', 'Price', 'Volume'});
        
        fprintf('   ✅ Staženo %d řádků pro %s\n', height(data), coinId);
        
    catch err
        fprintf('   ❌ Chyba při stahování %s: %s\n', coinId, err.message);
        data = table();
    end
end

%% Stahování dat pro všechny symboly
symbols = cryptoMapping.keys;
totalSymbols = length(symbols);

for i = 1:totalSymbols
    symbol = symbols{i};
    coinId = cryptoMapping(symbol);
    
    fprintf('🔄 [%d/%d] Zpracovávám: %s (%s)\n', i, totalSymbols, symbol, coinId);
    
    try
        % Stažení dat za posledních 365 dní (maximum pro hodinová data zdarma)
        data = getCoinGeckoData(coinId, 365);
        
        if isempty(data)
            warning('❌ %s: Žádná data stažena.', symbol);
            continue;
        end
        
        % Konverze na timetable
        tt = table2timetable(data);
        baseName = strrep(symbol, '/', '_');
        
        % Vytvoření OHLC dat z price dat (aproximace)
        % Pro skutečná OHLC data by bylo potřeba placené API
        tt.Open = tt.Price;
        tt.High = tt.Price;
        tt.Low = tt.Price;
        tt.Close = tt.Price;
        
        % Agregace na různé timeframy
        timeframes = {'1H', '1D'};
        steps = {hours(1), days(1)};
        
        for j = 1:length(timeframes)
            tf = timeframes{j};
            step = steps{j};
            
            if strcmp(tf, '1H')
                ttAgg = tt;
            else
                % Denní agregace
                ttAgg = retime(tt, 'daily', 'mean');
                ttAgg.Open = retime(tt(:, "Open"), 'daily', @(x)x(1)).Open;
                ttAgg.High = retime(tt(:, "High"), 'daily', @max).High;
                ttAgg.Low = retime(tt(:, "Low"), 'daily', @min).Low;
                ttAgg.Close = retime(tt(:, "Close"), 'daily', @(x)x(end)).Close;
                ttAgg.Volume = retime(tt(:, "Volume"), 'daily', @sum).Volume;
            end
            
            % Uložení .parquet
            parquetPath = fullfile(outDirParquet, sprintf('%s_%s.parquet', baseName, tf));
            tParq = timetable2table(ttAgg);
            writetable(tParq, parquetPath, 'FileType', 'parquet');
            fprintf('   💾 Parquet: %s\n', parquetPath);
            
            % Uložení .mat
            matPath = fullfile(outDirMat, sprintf('%s_%s.mat', baseName, tf));
            dataStruct = struct();
            dataStruct.(sprintf('%s_%s', baseName, tf)) = ttAgg;
            save(matPath, '-struct', 'dataStruct');
            fprintf('   💾 MAT: %s\n', matPath);
        end
        
        % Krátká pauza mezi požadavky (rate limiting)
        pause(0.5);
        
    catch ME
        warning('❌ [%s] Nezpracováno: %s', symbol, ME.message);
    end
end

fprintf('\n✅ Alternativní stahování dokončeno!\n');
fprintf('📁 Data uložena v: %s\n', baseDir);
fprintf('\n💡 Poznámky:\n');
fprintf('- Použita byla CoinGecko API (zdarma)\n');
fprintf('- Data jsou hodinová za posledních 365 dní\n');
fprintf('- Pro minutová data nebo delší historii je potřeba placené API\n');
fprintf('- OHLC data jsou aproximována z price dat\n');
