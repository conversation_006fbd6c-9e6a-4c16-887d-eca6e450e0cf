%% === Finální skript pro stahování krypto dat z Binance ===
%% Nahrazuje money_net_data.m - používá MATLAB-Binance-API

% Výstupní složky
baseDir = 'E:\02_Data\Binance_data';
outDirParquet = fullfile(baseDir, 'parquet');
outDirMat = fullfile(baseDir, 'mat');

if ~exist(outDirParquet, 'dir'), mkdir(outDirParquet); end
if ~exist(outDirMat, 'dir'), mkdir(outDirMat); end

% Seznam Top 20 krypto párů (Binance formát)
cryptoSymbols = {'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT', 'XRPUSDT', ...
                 'DOGEUSDT', 'ADAUSDT', 'AVAXUSDT', 'DOTUSDT', 'TRXUSDT', ...
                 'LINKUSDT', 'MATICUSDT', 'LTCUSDT', 'BCHUSDT', 'XLMUSDT', ...
                 'ATOMUSDT', 'ETCUSDT', 'HBARUSDT', 'NEARUSDT', 'ICPUSDT'};

% Intervaly pro stahování
intervals = {'1m', '5m', '15m', '1h', '1d'};

fprintf('🚀 Spouštím stahování krypto dat z Binance API...\n');
fprintf('📊 Symboly: %d\n', length(cryptoSymbols));
fprintf('⏱️  Intervaly: %s\n', strjoin(intervals, ', '));
fprintf('📁 Výstup: %s\n\n', baseDir);

%% === Test Binance API připojení ===
fprintf('🔍 Testování Binance API...\n');
try
    serverTime = pub.getServerTime();
    fprintf('✅ Binance API je dostupné\n');
    
    % Test stažení dat
    testData = pub.klines('BTCUSDT', '1h');
    fprintf('✅ Test stažení: %d řádků\n\n', height(testData));
    
catch err
    error('❌ Binance API není dostupné: %s', err.message);
end

%% === Stahování dat pro všechny symboly ===
totalSymbols = length(cryptoSymbols);
totalIntervals = length(intervals);
totalTasks = totalSymbols * totalIntervals;
currentTask = 0;

for i = 1:totalSymbols
    symbol = cryptoSymbols{i};
    fprintf('🔄 [%d/%d] Symbol: %s\n', i, totalSymbols, symbol);
    
    for j = 1:totalIntervals
        interval = intervals{j};
        currentTask = currentTask + 1;
        
        fprintf('  📊 [%d/%d] Interval: %s', currentTask, totalTasks, interval);
        
        try
            % Stažení dat pomocí pub.klines (posledních 500 záznamů)
            T = pub.klines(symbol, interval);
            
            if isempty(T)
                fprintf(' ❌ Žádná data\n');
                continue;
            end
            
            % Konverze timetable na table
            data = timetable2table(T);
            
            % Přejmenování Time na Timestamp
            if ismember('Time', data.Properties.VariableNames)
                data.Properties.VariableNames{'Time'} = 'Timestamp';
            end
            
            % Vytvoření názvu souboru
            baseName = symbol;
            
            % === Uložení .parquet ===
            parquetPath = fullfile(outDirParquet, sprintf('%s_%s.parquet', baseName, interval));
            try
                parquetwrite(parquetPath, data);
            catch
                % Fallback pro starší verze
                writetable(data, parquetPath);
            end
            
            % === Uložení .mat ===
            matPath = fullfile(outDirMat, sprintf('%s_%s.mat', baseName, interval));
            tt = table2timetable(data);
            dataStruct = struct();
            dataStruct.(sprintf('%s_%s', baseName, interval)) = tt;
            save(matPath, '-struct', 'dataStruct');
            
            fprintf(' ✅ %d řádků\n', height(data));
            
            % Rate limiting - krátká pauza
            pause(0.1);
            
        catch ME
            fprintf(' ❌ Chyba: %s\n', ME.message);
        end
    end
    
    % Delší pauza mezi symboly
    pause(0.5);
end

fprintf('\n🎉 Stahování dokončeno!\n');
fprintf('📁 Data uložena v: %s\n', baseDir);

%% === Statistiky ===
fprintf('\n📊 Statistiky:\n');

% Spočítáme soubory
parquetFiles = dir(fullfile(outDirParquet, '*.parquet'));
matFiles = dir(fullfile(outDirMat, '*.mat'));

fprintf('- Parquet soubory: %d\n', length(parquetFiles));
fprintf('- MAT soubory: %d\n', length(matFiles));
fprintf('- Symboly: %d\n', totalSymbols);
fprintf('- Intervaly: %s\n', strjoin(intervals, ', '));

% Ukázka velikostí souborů
if ~isempty(parquetFiles)
    totalSizeParquet = sum([parquetFiles.bytes]) / 1024 / 1024; % MB
    fprintf('- Celková velikost Parquet: %.1f MB\n', totalSizeParquet);
end

if ~isempty(matFiles)
    totalSizeMat = sum([matFiles.bytes]) / 1024 / 1024; % MB
    fprintf('- Celková velikost MAT: %.1f MB\n', totalSizeMat);
end

fprintf('\n💡 Poznámky:\n');
fprintf('- Každý soubor obsahuje posledních 500 záznamů pro daný interval\n');
fprintf('- Pro delší historii použijte pub.klines s časovým rozsahem\n');
fprintf('- Data jsou přímo z Binance burzy (real-time)\n');
fprintf('- Žádné API klíče nejsou potřeba pro tato data\n');

fprintf('\n✅ Hotovo! Data jsou připravena k analýze.\n');
