%% Test s 3 symboly a 2 intervaly
baseDir = 'test_output_3symbols';
outDirParquet = fullfile(baseDir, 'parquet');
outDirMat = fullfile(baseDir, 'mat');

if ~exist(outDirParquet, 'dir'), mkdir(outDirParquet); end
if ~exist(outDirMat, 'dir'), mkdir(outDirMat); end

% Pouze 3 symboly pro test
cryptoSymbols = {'BTCUSDT', 'ETHUSDT', 'BNBUSDT'};
intervals = {'1h', '1d'};

fprintf('🧪 Test stahování 3 symbolů...\n');

for i = 1:length(cryptoSymbols)
    symbol = cryptoSymbols{i};
    fprintf('🔄 [%d/%d] %s\n', i, length(cryptoSymbols), symbol);
    
    for j = 1:length(intervals)
        interval = intervals{j};
        fprintf('  📊 %s', interval);
        
        try
            T = pub.klines(symbol, interval);
            data = timetable2table(T);
            
            if ismember('Time', data.Properties.VariableNames)
                data.Properties.VariableNames{'Time'} = 'Timestamp';
            end
            
            % Parquet
            parquetPath = fullfile(outDirParquet, sprintf('%s_%s.parquet', symbol, interval));
            parquetwrite(parquetPath, data);
            
            % MAT
            matPath = fullfile(outDirMat, sprintf('%s_%s.mat', symbol, interval));
            tt = table2timetable(data);
            save(matPath, 'tt');
            
            fprintf(' ✅ %d řádků\n', height(data));
            pause(0.2);
            
        catch ME
            fprintf(' ❌ %s\n', ME.message);
        end
    end
end

fprintf('\n🎉 Test dokončen!\n');
