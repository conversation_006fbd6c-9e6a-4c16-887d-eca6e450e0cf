%% Test pub.repKlines pro delší historie
fprintf('🔄 Test pub.repKlines pro delší minutové historie...\n\n');

symbol = 'BTCUSDT';
interval = '1m';

% Test různ<PERSON>ch časových rozsahů
tests = {
    struct('name', '2 hodiny', 'hours', 2),
    struct('name', '12 hodin', 'hours', 12),
    struct('name', '24 hodin', 'hours', 24),
    struct('name', '3 dny', 'days', 3)
};

for i = 1:length(tests)
    test = tests{i};
    fprintf('📊 Test: %s\n', test.name);
    
    % Vytvoření časového roz<PERSON>hu
    if isfield(test, 'hours')
        startTime = datetime('now') - hours(test.hours);
        expectedMinutes = test.hours * 60;
    else
        startTime = datetime('now') - days(test.days);
        expectedMinutes = test.days * 24 * 60;
    end
    endTime = datetime('now');
    
    fprintf('   Od: %s\n', char(startTime));
    fprintf('   Do: %s\n', char(endTime));
    fprintf('   Očekáváno: %d minut\n', expectedMinutes);
    
    try
        tic;
        T = pub.repKlines(symbol, interval, [startTime, endTime]);
        downloadTime = toc;
        
        if ~isempty(T)
            actualStart = T.Time(1);
            actualEnd = T.Time(end);
            actualMinutes = height(T);
            coverage = (actualMinutes / expectedMinutes) * 100;
            
            fprintf('   ✅ Staženo: %d řádků za %.1f sekund\n', actualMinutes, downloadTime);
            fprintf('   📅 Skutečně od: %s\n', char(actualStart));
            fprintf('   📅 Skutečně do: %s\n', char(actualEnd));
            fprintf('   📊 Pokrytí: %.1f%% (%d/%d minut)\n', coverage, actualMinutes, expectedMinutes);
            
            % Kontrola kontinuity dat
            timeDiffs = diff(T.Time);
            expectedDiff = minutes(1);
            missingData = sum(timeDiffs > expectedDiff * 1.5); % tolerance
            
            if missingData == 0
                fprintf('   ✅ Data jsou kontinuální\n');
            else
                fprintf('   ⚠️  Nalezeno %d mezer v datech\n', missingData);
            end
            
        else
            fprintf('   ❌ Žádná data\n');
        end
        
    catch err
        fprintf('   ❌ Chyba: %s\n', err.message);
    end
    
    fprintf('\n');
    
    % Rate limiting - delší pauza pro větší požadavky
    if i < length(tests)
        pause(2);
    end
end

fprintf('📋 Shrnutí pro minutová data:\n');
fprintf('- pub.klines(): max 500 minut (~8.3 hodin)\n');
fprintf('- pub.repKlines(): teoreticky neomezeno, ale pomalé\n');
fprintf('- Pro dlouhé historie: použijte pub.repKlines()\n');
fprintf('- Pro rychlé testování: použijte pub.klines()\n\n');

fprintf('💡 Doporučení pro váš původní skript:\n');
fprintf('- Pro minutová data: použijte pub.repKlines() s časovým rozsahem\n');
fprintf('- Pro hodinová/denní data: pub.klines() stačí (500 záznamů = 20+ dní)\n');
