%% Rychlý test Binance API
fprintf('🚀 Rychlý test Binance API...\n');

% Test základní <PERSON>
try
    fprintf('Testuji pub.getServerTime()...\n');
    serverTime = pub.getServerTime();
    fprintf('✅ Server čas: %s\n', datetime(serverTime/1000, 'ConvertFrom', 'posixtime'));
    
    fprintf('Testuji pub.price()...\n');
    price = pub.price('BTCUSDT');
    fprintf('✅ BTC cena: $%s\n', price.price);
    
    fprintf('Testuji pub.klines()...\n');
    klines = pub.klines('BTCUSDT', '1h', 'limit', 5);
    fprintf('✅ Klines: %d řádků\n', size(klines, 1));
    
    fprintf('\n🎉 Všechny testy prošly! API funguje správně.\n');
    
catch err
    fprintf('❌ Chyba: %s\n', err.message);
    fprintf('💡 Zkontrolujte:\n');
    fprintf('   - Je MATLAB-Binance-API správně nainstalováno?\n');
    fprintf('   - Je přidáno do MATLAB path?\n');
    fprintf('   - Funguje internetové připojení?\n');
end
