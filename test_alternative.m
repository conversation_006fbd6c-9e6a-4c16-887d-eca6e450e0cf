%% Rychlý test alternativního řešení
fprintf('🧪 Testování alternativního řešení s CoinGecko API...\n\n');

% Test stažení dat pro Bitcoin
fprintf('📊 Test stažení dat pro Bitcoin...\n');

try
    url = 'https://api.coingecko.com/api/v3/coins/bitcoin/market_chart';
    params = {'vs_currency', 'usd', 'days', '7', 'interval', 'hourly'};
    
    fprintf('   Stahování dat...\n');
    response = webread(url, params{:}, 'Timeout', 30);
    
    % Zpracování dat
    prices = response.prices;
    volumes = response.total_volumes;
    
    timestamps = datetime(prices(:,1)/1000, 'ConvertFrom', 'posixtime');
    priceValues = prices(:,2);
    volumeValues = volumes(:,2);
    
    data = table(timestamps, priceValues, volumeValues, ...
                'VariableNames', {'Timestamp', 'Price', 'Volume'});
    
    fprintf('✅ Úspěšně staženo %d řádků dat\n', height(data));
    fprintf('📅 Časový rozsah: %s až %s\n', ...
            char(min(data.Timestamp)), char(max(data.Timestamp)));
    fprintf('💰 Cenový rozsah: $%.2f - $%.2f\n', ...
            min(data.Price), max(data.Price));
    
    % Ukázka dat
    fprintf('\n📋 Ukázka prvních 5 řádků:\n');
    disp(head(data, 5));
    
    % Test uložení
    testDir = 'test_output';
    if ~exist(testDir, 'dir'), mkdir(testDir); end
    
    % Uložení jako parquet
    parquetPath = fullfile(testDir, 'BTC_USD_test.parquet');
    writetable(data, parquetPath, 'FileType', 'parquet');
    fprintf('💾 Test soubor uložen: %s\n', parquetPath);
    
    fprintf('\n✅ Test alternativního řešení ÚSPĚŠNÝ!\n');
    fprintf('💡 Můžete nyní spustit crypto_data_alternative.m pro stažení všech dat\n');
    
catch err
    fprintf('❌ Test selhal: %s\n', err.message);
    fprintf('🔧 Zkontrolujte internetové připojení a dostupnost CoinGecko API\n');
end
