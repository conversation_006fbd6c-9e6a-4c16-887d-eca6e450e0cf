%% === Vylepšená verze s lepším error handling ===
%% === Uživatelské nastavení ===
username = '<EMAIL>';
password = 'pkm7fvb2UAR-wdk*fex';

% Výstupní složky
baseDir = 'E:\02_Data\Money_net';
outDirParquet = fullfile(baseDir, 'parquet');
outDirMat = fullfile(baseDir, 'mat');

if ~exist(outDirParquet, 'dir'), mkdir(outDirParquet); end
if ~exist(outDirMat, 'dir'), mkdir(outDirMat); end

% Rozsah a interval dat
startDate = datetime('2024-01-01');
endDate = datetime('now');
interval = '1M';               % 1minutový interval
chunkSize = calmonths(3);      % stahování po 3měsíčních blocích

% Seznam Top 20 krypto párů
cryptoSymbols = {'BTC/USD', 'ETH/USD', 'BNB/USD', 'SOL/USD', 'XRP/USD', ...
                 'DOGE/USD', 'ADA/USD', 'AVAX/USD', 'DOT/USD', 'TRX/USD', ...
                 'LINK/USD', 'MATIC/USD', 'LTC/USD', 'BCH/USD', 'XLM/USD', ...
                 'ATOM/USD', 'ETC/USD', 'HBAR/USD', 'NEAR/USD', 'ICP/USD'};

% ✅ OPRAVENÁ map() definice s cell array hodnotami
tfMap = containers.Map( ...
    {'1m', '5m', '15m', '1h', '1d'}, ...
    {minutes(1), minutes(5), minutes(15), hours(1), days(1)} ...
);

%% === Detailní diagnostika před spuštěním ===
fprintf('\n🔍 Spouštím detailní diagnostiku...\n');

% Kontrola Datafeed Toolbox
if exist('moneynet', 'file') ~= 2
    error('❌ Funkce moneynet není dostupná. Zkontrolujte instalaci Datafeed Toolbox.');
end

% Kontrola internetového připojení
try
    webread('https://www.google.com', 'Timeout', 5);
    fprintf('✅ Internetové připojení funguje\n');
catch
    error('❌ Žádné internetové připojení!');
end

%% === Pokročilé testování připojení k Money.Net ===
fprintf('\n🔐 Testování připojení k Money.Net s detailní diagnostikou...\n');

maxRetries = 3;
retryDelay = 5; % sekund
connectionSuccess = false;

for attempt = 1:maxRetries
    fprintf('   Pokus %d/%d...\n', attempt, maxRetries);
    
    try
        c = moneynet(username, password);
        
        % Test s různými symboly a časovými rozsahy
        testSymbols = {'BTC/USD', 'ETH/USD'};
        testRanges = {
            [datetime('now') - days(1), datetime('now')], ...
            [datetime('now') - days(7), datetime('now') - days(6)]
        };
        testIntervals = {'1D', '1H'};
        
        testPassed = false;
        for i = 1:length(testSymbols)
            for j = 1:length(testRanges)
                for k = 1:length(testIntervals)
                    try
                        fprintf('      Test: %s, %s, %s\n', ...
                                testSymbols{i}, ...
                                char(testRanges{j}(1)), ...
                                testIntervals{k});
                        
                        testData = timeseries(c, testSymbols{i}, ...
                                            testRanges{j}(1), testRanges{j}(2), ...
                                            testIntervals{k});
                        
                        if ~isempty(testData)
                            fprintf('      ✅ Test úspěšný - %d řádků\n', height(testData));
                            testPassed = true;
                            break;
                        else
                            fprintf('      ⚠️  Test vrátil prázdná data\n');
                        end
                    catch testErr
                        fprintf('      ❌ Test selhal: %s\n', testErr.message);
                    end
                end
                if testPassed, break; end
            end
            if testPassed, break; end
        end
        
        if testPassed
            fprintf('✅ Připojení k Money.Net úspěšné!\n');
            connectionSuccess = true;
            close(c);
            break;
        else
            fprintf('⚠️  Připojení navázáno, ale žádný test neprošel\n');
            close(c);
        end
        
    catch connErr
        fprintf('   ❌ Pokus %d selhal: %s\n', attempt, connErr.message);
        
        % Detailní analýza chyby
        if contains(connErr.message, 'Unexpected error', 'IgnoreCase', true)
            fprintf('   🔍 Pravděpodobně problém se serverem Money.Net nebo účtem\n');
            fprintf('   💡 Doporučení:\n');
            fprintf('      - Zkontrolujte stav účtu na webu Money.Net\n');
            fprintf('      - Kontaktujte podporu Money.Net\n');
            fprintf('      - Zkuste jiné přihlašovací údaje (pokud máte)\n');
        end
        
        if attempt < maxRetries
            fprintf('   ⏳ Čekám %d sekund před dalším pokusem...\n', retryDelay);
            pause(retryDelay);
        end
    end
end

if ~connectionSuccess
    fprintf('\n❌ Všechny pokusy o připojení selhaly!\n');
    fprintf('\n🔧 MOŽNÁ ŘEŠENÍ:\n');
    fprintf('1. Zkontrolujte přihlašovací údaje na webu Money.Net\n');
    fprintf('2. Ověřte, zda máte aktivní licenci pro API přístup\n');
    fprintf('3. Kontaktujte technickou podporu Money.Net\n');
    fprintf('4. Zkuste použít alternativní zdroje dat (viz moneynet_troubleshooting.m)\n');
    fprintf('\n📧 Kontakt na Money.Net podporu:\n');
    fprintf('   - Web: https://www.money.net/support\n');
    fprintf('   - Email: <EMAIL> (ověřte si aktuální kontakt)\n');
    
    % Nabídka alternativního řešení
    fprintf('\n🔄 Chcete pokračovat s alternativním zdrojem dat? (y/n): ');
    userChoice = input('', 's');
    
    if strcmpi(userChoice, 'y') || strcmpi(userChoice, 'yes')
        fprintf('\n📝 Spouštím alternativní řešení...\n');
        % Zde by bylo volání alternativní funkce
        fprintf('💡 Implementujte alternativní zdroj dat (CoinGecko, Alpha Vantage, atd.)\n');
    end
    
    return; % Ukončení skriptu
end

%% === Původní kód pokračuje pouze pokud je připojení úspěšné ===
fprintf('\n🚀 Spouštím stahování dat...\n');

%% === Spuštění paralelního poolu ===
if isempty(gcp('nocreate'))
    parpool('local');
end

%% === Paralelní zpracování všech symbolů ===
parfor i = 1:length(cryptoSymbols)
    symbol = cryptoSymbols{i};
    fprintf('\n🔄 [%d/%d] Zpracovávám: %s\n', i, length(cryptoSymbols), symbol);

    try
        c_local = moneynet(username, password);  % nové připojení pro každý worker
        allData = table();

        % Rozdělení na časové bloky
        ranges = startDate:chunkSize:endDate;
        if ranges(end) < endDate
            ranges = [ranges, endDate];
        end

        for r = 1:(length(ranges)-1)
            fprintf('   ⏳ Chunk %s - %s\n', datestr(ranges(r)), datestr(ranges(r+1)));
            try
                d = timeseries(c_local, symbol, [ranges(r), ranges(r+1)], interval);
                allData = [allData; d];
            catch eChunk
                warning('   ⚠️ Chyba chunk %s-%s: %s', ...
                        datestr(ranges(r)), datestr(ranges(r+1)), eChunk.message);
            end
        end

        if isempty(allData)
            warning('❌ %s: Žádná data stažena.', symbol);
            continue;
        end

        allData.Timestamp = datetime(allData.Timestamp);
        tt = table2timetable(allData);
        baseName = strrep(symbol, '/', '_');

        tfKeys = tfMap.keys;
        for j = 1:length(tfKeys)
            tf = tfKeys{j};
            step = tfMap(tf);

            % Agregace
            if strcmp(tf, '1m')
                ttAgg = tt;
            else
                ttAgg = retime(tt, 'regular', @(x) nan, 'TimeStep', step);
                ttAgg.Open   = retime(tt(:, "Open"),   'regular', @(x)x(1), 'TimeStep', step).Open;
                ttAgg.High   = retime(tt(:, "High"),   'regular', @max,     'TimeStep', step).High;
                ttAgg.Low    = retime(tt(:, "Low"),    'regular', @min,     'TimeStep', step).Low;
                ttAgg.Close  = retime(tt(:, "Close"),  'regular', @(x)x(end),'TimeStep', step).Close;
                ttAgg.Volume = retime(tt(:, "Volume"), 'regular', @sum,     'TimeStep', step).Volume;
            end

            % === Uložení .parquet ===
            parquetPath = fullfile(outDirParquet, sprintf('%s_%s.parquet', baseName, tf));
            tParq = timetable2table(ttAgg);
            writetable(tParq, parquetPath, 'FileType', 'parquet');
            fprintf('   💾 Parquet: %s\n', parquetPath);

            % === Uložení .mat ===
            matPath = fullfile(outDirMat, sprintf('%s_%s.mat', baseName, tf));
            dataStruct = struct();
            dataStruct.(sprintf('%s_%s', baseName, tf)) = ttAgg;
            save(matPath, '-struct', 'dataStruct');
            fprintf('   💾 MAT:     %s\n', matPath);
        end

        close(c_local);
    catch ME
        warning('❌ [%s] Nezpracováno: %s', symbol, ME.message);
    end
end

fprintf('\n✅ Zpracování dokončeno!\n');
