%% Test Binance API připojení a funkcí
fprintf('🧪 Testování MATLAB-Binance-API...\n\n');

%% Test 1: <PERSON>áklad<PERSON><PERSON> připojení
fprintf('1. Test základního připojení...\n');
try
    serverTime = pub.getServerTime();
    serverDateTime = datetime(serverTime/1000, 'ConvertFrom', 'posixtime');
    fprintf('✅ Server čas: %s\n', char(serverDateTime));
catch err
    fprintf('❌ Chyba připojení: %s\n', err.message);
    return;
end

%% Test 2: Exchange info
fprintf('\n2. Test exchange info...\n');
try
    exchangeInfo = pub.exchangeInfo();
    fprintf('✅ Exchange info získáno\n');
    fprintf('   Počet symbolů: %d\n', length(exchangeInfo.symbols));
    
    % Najdeme BTCUSDT symbol
    btcSymbol = [];
    for i = 1:length(exchangeInfo.symbols)
        if strcmp(exchangeInfo.symbols{i}.symbol, 'BTCUSDT')
            btcSymbol = exchangeInfo.symbols{i};
            break;
        end
    end
    
    if ~isempty(btcSymbol)
        fprintf('   BTCUSDT symbol nalezen ✅\n');
        fprintf('   Status: %s\n', btcSymbol.status);
    else
        fprintf('   ⚠️ BTCUSDT symbol nenalezen\n');
    end
    
catch err
    fprintf('❌ Chyba exchange info: %s\n', err.message);
end

%% Test 3: Aktuální cena
fprintf('\n3. Test aktuální ceny...\n');
try
    price = pub.price('BTCUSDT');
    fprintf('✅ BTC/USDT cena: $%s\n', price.price);
catch err
    fprintf('❌ Chyba získání ceny: %s\n', err.message);
end

%% Test 4: Klines (historická data)
fprintf('\n4. Test historických dat (klines)...\n');
try
    % Stažení posledních 10 hodinových svíček pro BTCUSDT
    endTime = datetime('now');
    startTime = endTime - hours(10);
    
    startTs = int64(posixtime(startTime) * 1000);
    endTs = int64(posixtime(endTime) * 1000);
    
    klines = pub.klines('BTCUSDT', '1h', 'startTime', startTs, 'endTime', endTs, 'limit', 10);
    
    if ~isempty(klines)
        fprintf('✅ Historická data stažena: %d svíček\n', size(klines, 1));
        
        % Konverze na tabulku pro ukázku
        timestamps = datetime(klines(:,1)/1000, 'ConvertFrom', 'posixtime');
        opens = klines(:,2);
        highs = klines(:,3);
        lows = klines(:,4);
        closes = klines(:,5);
        volumes = klines(:,6);
        
        data = table(timestamps, opens, highs, lows, closes, volumes, ...
                    'VariableNames', {'Timestamp', 'Open', 'High', 'Low', 'Close', 'Volume'});
        
        fprintf('   📋 Ukázka posledních 3 řádků:\n');
        disp(tail(data, 3));
        
    else
        fprintf('⚠️ Žádná historická data\n');
    end
    
catch err
    fprintf('❌ Chyba historických dat: %s\n', err.message);
end

%% Test 5: Více symbolů najednou
fprintf('\n5. Test více symbolů...\n');
try
    symbols = {'BTCUSDT', 'ETHUSDT', 'BNBUSDT'};
    prices = pub.price(symbols);
    
    fprintf('✅ Ceny více symbolů:\n');
    for i = 1:length(prices)
        fprintf('   %s: $%s\n', prices{i}.symbol, prices{i}.price);
    end
    
catch err
    fprintf('❌ Chyba více symbolů: %s\n', err.message);
end

%% Test 6: Rate limiting test
fprintf('\n6. Test rate limiting...\n');
try
    fprintf('   Testuji rychlé po sobě jdoucí požadavky...\n');
    
    tic;
    for i = 1:5
        price = pub.price('BTCUSDT');
        fprintf('   Požadavek %d: $%s\n', i, price.price);
        pause(0.1); % Krátká pauza
    end
    elapsed = toc;
    
    fprintf('✅ Rate limiting test dokončen za %.2f sekund\n', elapsed);
    
catch err
    fprintf('❌ Chyba rate limiting: %s\n', err.message);
end

fprintf('\n=== VÝSLEDEK TESTŮ ===\n');
fprintf('Pokud všechny testy prošly, můžete spustit binance_crypto_data.m\n');
fprintf('pro stažení kompletních historických dat.\n\n');

fprintf('💡 Tipy:\n');
fprintf('- Binance API má rate limity (1200 requests/minute)\n');
fprintf('- Pro velké objemy dat použijte pauzy mezi požadavky\n');
fprintf('- Veřejné endpointy nevyžadují API klíče\n');
fprintf('- Pro trading operace budete potřebovat API klíče\n');
