%% Stahování delší historie pro minutová data
fprintf('📈 Stahování delší historie pro minutová data...\n\n');

symbol = 'BTCUSDT';
interval = '1m';

% Různé časové rozsahy
timeRanges = {
    struct('name', '24 hodin', 'start', datetime('now') - hours(24), 'end', datetime('now')),
    struct('name', '7 dní', 'start', datetime('now') - days(7), 'end', datetime('now')),
    struct('name', '30 dní', 'start', datetime('now') - days(30), 'end', datetime('now'))
};

for i = 1:length(timeRanges)
    range = timeRanges{i};
    fprintf('🕐 Test: %s\n', range.name);
    fprintf('   Od: %s\n', char(range.start));
    fprintf('   Do: %s\n', char(range.end));
    
    try
        % Stažen<PERSON> s časovým rozsahem
        T = pub.klines(symbol, interval, [range.start, range.end]);
        
        if ~isempty(T)
            actualStart = T.Time(1);
            actualEnd = T.Time(end);
            actualDuration = actualEnd - actualStart;
            
            fprintf('   ✅ Staženo: %d řádků\n', height(T));
            fprintf('   📅 Skutečně od: %s\n', char(actualStart));
            fprintf('   📅 Skutečně do: %s\n', char(actualEnd));
            fprintf('   ⏱️  Skutečná délka: %s\n', char(actualDuration));
            
            % Výpočet pokrytí
            requestedMinutes = minutes(range.end - range.start);
            actualMinutes = height(T);
            coverage = (actualMinutes / requestedMinutes) * 100;
            fprintf('   📊 Pokrytí: %.1f%% (%d/%d minut)\n', coverage, actualMinutes, requestedMinutes);
            
        else
            fprintf('   ❌ Žádná data\n');
        end
        
    catch err
        fprintf('   ❌ Chyba: %s\n', err.message);
    end
    
    fprintf('\n');
    pause(1); % Rate limiting pro delší požadavky
end

fprintf('💡 Pozorování:\n');
fprintf('- Binance má limity na historická data\n');
fprintf('- Pro minutová data: obvykle max 1000 záznamů na požadavek\n');
fprintf('- Pro delší historii je potřeba více požadavků (chunking)\n');
fprintf('- Některá velmi stará data nemusí být dostupná\n\n');

fprintf('🔧 Pro stažení kompletní historie použijte:\n');
fprintf('   pub.repKlines() - opakované volání pro velké rozsahy\n');
fprintf('   nebo implementujte vlastní chunking\n');
