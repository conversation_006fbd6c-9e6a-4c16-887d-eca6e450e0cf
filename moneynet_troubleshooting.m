%% Money.Net Troubleshooting a alternativní řešení
fprintf('=== MONEY.NET TROUBLESHOOTING ===\n\n');

%% Možné příčiny a řešení problému s přihlášením

fprintf('IDENTIFIKOVANÝ PROBLÉM:\n');
fprintf('- Chyba: "Unexpected error while logging in to Money.Net"\n');
fprintf('- Všechny systémové komponenty fungují správně\n');
fprintf('- Problém je pravděpodobně na straně Money.Net serveru nebo účtu\n\n');

fprintf('DOPORUČENÁ ŘEŠENÍ:\n\n');

fprintf('1. OVĚŘENÍ PŘIHLAŠOVACÍCH ÚDAJŮ:\n');
fprintf('   - Zkontrolujte si přihlašovací údaje na webu Money.Net\n');
fprintf('   - Ujistěte se, že účet nen<PERSON>lo<PERSON>\n');
fprintf('   - <PERSON><PERSON>te se přihlásit přes webové rozhraní\n\n');

fprintf('2. KONTAKT S MONEY.NET PODPOROU:\n');
fprintf('   - Kontaktujte technickou podporu Money.Net\n');
fprintf('   - Informujte je o chybě při API přístupu z MATLABu\n');
fprintf('   - Ověřte si, zda máte aktivní licenci pro API přístup\n\n');

fprintf('3. ALTERNATIVNÍ PŘÍSTUPY K DATŮM:\n');
fprintf('   a) Ruční export z Money.Net webu\n');
fprintf('   b) Použití jiných finančních API (Alpha Vantage, Yahoo Finance)\n');
fprintf('   c) Použití specializovaných knihoven pro krypto data\n\n');

%% Test alternativních zdrojů dat
fprintf('TESTOVÁNÍ ALTERNATIVNÍCH ZDROJŮ:\n\n');

% Test Yahoo Finance (pokud je dostupný)
fprintf('Test Yahoo Finance přístupu...\n');
try
    % Pokus o stažení dat z Yahoo Finance
    symbol = 'BTC-USD';
    startDate = datetime('now') - days(7);
    endDate = datetime('now');
    
    % Poznámka: Toto vyžaduje Financial Toolbox nebo custom funkci
    if exist('getMarketDataViaYahoo', 'file') == 2
        data = getMarketDataViaYahoo(symbol, startDate, endDate);
        fprintf('✅ Yahoo Finance funguje - %d řádků dat\n', height(data));
    else
        fprintf('⚠️  Yahoo Finance funkce není dostupná\n');
        fprintf('   Můžete použít webread() pro přímé API volání\n');
    end
catch
    fprintf('❌ Yahoo Finance nedostupný\n');
end

fprintf('\n');

%% Ukázka alternativního řešení s webread()
fprintf('UKÁZKA ALTERNATIVNÍHO ŘEŠENÍ:\n');
fprintf('Můžete použít přímé API volání místo Money.Net:\n\n');

% Ukázka kódu pro alternativní zdroj
fprintf('Příklad kódu pro CoinGecko API:\n');
fprintf('url = ''https://api.coingecko.com/api/v3/coins/bitcoin/market_chart'';\n');
fprintf('params = {''vs_currency'', ''usd'', ''days'', ''30''};\n');
fprintf('data = webread(url, params{:});\n\n');

% Test CoinGecko API
fprintf('Test CoinGecko API...\n');
try
    url = 'https://api.coingecko.com/api/v3/ping';
    response = webread(url, 'Timeout', 10);
    if isfield(response, 'gecko_says')
        fprintf('✅ CoinGecko API je dostupné\n');
        fprintf('   Odpověď: %s\n', response.gecko_says);
    end
catch
    fprintf('❌ CoinGecko API nedostupný\n');
end

fprintf('\n=== DOPORUČENÍ ===\n');
fprintf('1. Kontaktujte Money.Net podporu ohledně API přístupu\n');
fprintf('2. Mezitím můžete použít alternativní zdroje dat\n');
fprintf('3. Zvažte použití více zdrojů dat pro redundanci\n');
fprintf('4. Implementujte error handling pro robustnější řešení\n\n');
