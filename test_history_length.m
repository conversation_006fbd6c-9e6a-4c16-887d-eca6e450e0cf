%% Test délky historie pro různé intervaly
fprintf('🕐 Testování délky historie pro různé intervaly...\n\n');

intervals = {'1m', '5m', '15m', '1h', '1d'};
symbol = 'BTCUSDT';

for i = 1:length(intervals)
    interval = intervals{i};
    fprintf('📊 Interval: %s\n', interval);
    
    try
        % Stažení dat
        T = pub.klines(symbol, interval);
        
        if ~isempty(T)
            % Analýza časového rozsahu
            startTime = T.Time(1);
            endTime = T.Time(end);
            duration = endTime - startTime;
            
            fprintf('   📅 Od: %s\n', char(startTime));
            fprintf('   📅 Do: %s\n', char(endTime));
            fprintf('   ⏱️  Délka: %s\n', char(duration));
            fprintf('   📊 Počet řádků: %d\n', height(T));
            
            % Výpočet teoretické délky pro 500 záznamů
            switch interval
                case '1m'
                    theoretical = "8h 20min (500 minut)";
                case '5m'
                    theoretical = "~41h 40min (500 × 5min)";
                case '15m'
                    theoretical = "~5.2 dní (500 × 15min)";
                case '1h'
                    theoretical = "~20.8 dní (500 hodin)";
                case '1d'
                    theoretical = "~1.4 roku (500 dní)";
                otherwise
                    theoretical = "N/A";
            end
            fprintf('   🧮 Teoreticky: %s\n', theoretical);
            
        else
            fprintf('   ❌ Žádná data\n');
        end
        
    catch err
        fprintf('   ❌ Chyba: %s\n', err.message);
    end
    
    fprintf('\n');
    pause(0.5); % Rate limiting
end

fprintf('💡 Poznámky:\n');
fprintf('- Bez časového rozsahu se stahuje posledních 500 záznamů\n');
fprintf('- Pro delší historii použijte timeRange parametr\n');
fprintf('- Binance má limity na množství historických dat\n');
fprintf('- Pro minutová data: max ~8.3 hodin bez timeRange\n');
