%% Diagnostický skript pro Money.Net připojení
fprintf('=== DIAGNOSTIKA MONEY.NET PŘIPOJENÍ ===\n\n');

%% 1. Kontrola instalace Datafeed Toolbox
fprintf('1. Kontrola instalace Datafeed Toolbox...\n');
try
    ver('datafeed')
    fprintf('✅ Datafeed Toolbox je nainstalován\n\n');
catch
    fprintf('❌ Datafeed Toolbox NENÍ nainstalován!\n');
    fprintf('   Řešení: Nainstalujte Datafeed Toolbox přes Add-On Explorer\n\n');
    return;
end

%% 2. Kontrola dostupnosti moneynet funkce
fprintf('2. Kontrola dostupnosti moneynet funkce...\n');
if exist('moneynet', 'file') == 2
    fprintf('✅ Funkce moneynet je dostupná\n\n');
else
    fprintf('❌ Funkce moneynet NENÍ dostupná!\n');
    fprintf('   Možné příčiny:\n');
    fprintf('   - Datafeed Toolbox není správně nainstalován\n');
    fprintf('   - Licence není aktivní\n');
    fprintf('   - MATLAB cesta není správně nastavena\n\n');
    return;
end

%% 3. Kontrola síťového připojení
fprintf('3. Kontrola síťového připojení...\n');
try
    % Test základního připojení k internetu
    webread('https://www.google.com', 'Timeout', 5);
    fprintf('✅ Internetové připojení funguje\n');
    
    % Test připojení k Money.Net serveru (pokud známe URL)
    try
        webread('https://www.money.net', 'Timeout', 10);
        fprintf('✅ Money.Net server je dostupný\n\n');
    catch
        fprintf('⚠️  Money.Net server možná není dostupný nebo blokován firewallem\n\n');
    end
catch
    fprintf('❌ Žádné internetové připojení!\n\n');
    return;
end

%% 4. Test přihlašovacích údajů
fprintf('4. Test přihlašovacích údajů...\n');
username = '<EMAIL>';
password = 'pkm7fvb2UAR-wdk*fex';

fprintf('   Username: %s\n', username);
fprintf('   Password: %s\n', repmat('*', 1, length(password)));

try
    fprintf('   Pokouším se připojit...\n');
    c = moneynet(username, password);
    fprintf('✅ Připojení úspěšné!\n');
    
    % Test základního dotazu
    fprintf('   Testuji základní dotaz...\n');
    try
        testData = timeseries(c, 'BTC/USD', datetime('now') - days(2), datetime('now'), '1D');
        if isempty(testData)
            fprintf('⚠️  Připojení OK, ale žádná data se nevrátila\n');
            fprintf('      Možné příčiny:\n');
            fprintf('      - Symbol BTC/USD není dostupný\n');
            fprintf('      - Časový rozsah je neplatný\n');
            fprintf('      - Účet nemá oprávnění k těmto datům\n');
        else
            fprintf('✅ Test dotaz vrátil %d řádků dat\n', height(testData));
            disp('   Ukázka dat:');
            disp(head(testData, 3));
        end
    catch testErr
        fprintf('❌ Test dotaz selhal: %s\n', testErr.message);
    end
    
    close(c);
    fprintf('\n');
    
catch connErr
    fprintf('❌ Připojení selhalo!\n');
    fprintf('   Chyba: %s\n', connErr.message);
    fprintf('   Možné příčiny:\n');
    fprintf('   - Nesprávné přihlašovací údaje\n');
    fprintf('   - Účet je zablokován nebo neaktivní\n');
    fprintf('   - Síťové problémy nebo firewall\n');
    fprintf('   - Money.Net server je nedostupný\n');
    fprintf('   - Licence Datafeed Toolbox vypršela\n\n');
    
    % Detailnější analýza chyby
    if contains(connErr.message, 'authentication', 'IgnoreCase', true) || ...
       contains(connErr.message, 'login', 'IgnoreCase', true) || ...
       contains(connErr.message, 'password', 'IgnoreCase', true)
        fprintf('   🔍 Pravděpodobně problém s autentizací\n');
    elseif contains(connErr.message, 'network', 'IgnoreCase', true) || ...
           contains(connErr.message, 'connection', 'IgnoreCase', true) || ...
           contains(connErr.message, 'timeout', 'IgnoreCase', true)
        fprintf('   🔍 Pravděpodobně síťový problém\n');
    elseif contains(connErr.message, 'license', 'IgnoreCase', true)
        fprintf('   🔍 Pravděpodobně problém s licencí\n');
    end
    fprintf('\n');
end

%% 5. Kontrola MATLAB verze a kompatibility
fprintf('5. Informace o MATLAB prostředí...\n');
fprintf('   MATLAB verze: %s\n', version);
fprintf('   Platforma: %s\n', computer);

% Kontrola dostupných toolboxů
installedToolboxes = ver;
datafeedIdx = find(contains({installedToolboxes.Name}, 'Datafeed', 'IgnoreCase', true));
if ~isempty(datafeedIdx)
    fprintf('   Datafeed Toolbox verze: %s\n', installedToolboxes(datafeedIdx).Version);
end

fprintf('\n=== KONEC DIAGNOSTIKY ===\n');
