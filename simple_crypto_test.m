%% Jednoduchý test různých crypto API
fprintf('🧪 Testování různých crypto API...\n\n');

%% Test 1: CoinGecko základní API
fprintf('1. Test CoinGecko základní API...\n');
try
    url = 'https://api.coingecko.com/api/v3/simple/price';
    params = {'ids', 'bitcoin,ethereum', 'vs_currencies', 'usd'};
    data = webread(url, params{:}, 'Timeout', 15);
    
    fprintf('✅ CoinGecko základní API funguje\n');
    fprintf('   Bitcoin: $%.2f\n', data.bitcoin.usd);
    fprintf('   Ethereum: $%.2f\n', data.ethereum.usd);
    
catch err
    fprintf('❌ CoinGecko základní API: %s\n', err.message);
end

%% Test 2: CoinCap API (alternativa)
fprintf('\n2. Test CoinCap API...\n');
try
    url = 'https://api.coincap.io/v2/assets/bitcoin';
    data = webread(url, 'Timeout', 15);
    
    fprintf('✅ CoinCap API funguje\n');
    fprintf('   Bitcoin: $%.2f\n', str2double(data.data.priceUsd));
    
catch err
    fprintf('❌ CoinCap API: %s\n', err.message);
end

%% Test 3: Binance API (veřejné)
fprintf('\n3. Test Binance API...\n');
try
    url = 'https://api.binance.com/api/v3/ticker/price';
    params = {'symbol', 'BTCUSDT'};
    data = webread(url, params{:}, 'Timeout', 15);
    
    fprintf('✅ Binance API funguje\n');
    fprintf('   BTC/USDT: $%.2f\n', str2double(data.price));
    
catch err
    fprintf('❌ Binance API: %s\n', err.message);
end

%% Test 4: Kraken API (veřejné)
fprintf('\n4. Test Kraken API...\n');
try
    url = 'https://api.kraken.com/0/public/Ticker';
    params = {'pair', 'XBTUSD'};
    data = webread(url, params{:}, 'Timeout', 15);
    
    if isfield(data, 'result') && isfield(data.result, 'XXBTZUSD')
        price = str2double(data.result.XXBTZUSD.c{1});
        fprintf('✅ Kraken API funguje\n');
        fprintf('   BTC/USD: $%.2f\n', price);
    else
        fprintf('⚠️  Kraken API odpověděl, ale data mají neočekávaný formát\n');
    end
    
catch err
    fprintf('❌ Kraken API: %s\n', err.message);
end

fprintf('\n=== DOPORUČENÍ ===\n');
fprintf('Na základě testů můžeme použít API, které funguje nejlépe.\n');
fprintf('Pro historická data doporučuji:\n');
fprintf('1. Binance API - má dobré historické endpointy\n');
fprintf('2. CoinCap API - jednoduchý a spolehlivý\n');
fprintf('3. Kraken API - profesionální, ale složitější\n\n');
