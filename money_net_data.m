%% === Uživatelské nastavení ===
username = '<EMAIL>';
password = 'pkm7fvb2UAR-wdk*fex';

% Výstupní složky
baseDir = 'E:\02_Data\Money_net';
outDirParquet = fullfile(baseDir, 'parquet');
outDirMat = fullfile(baseDir, 'mat');

if ~exist(outDirParquet, 'dir'), mkdir(outDirParquet); end
if ~exist(outDirMat, 'dir'), mkdir(outDirMat); end

% Rozsah a interval dat
startDate = datetime('2024-01-01');
endDate = datetime('now');
interval = '1M';               % 1minutový interval
chunkSize = calmonths(3);      % stahování po 3měsíčních blocích

% Seznam Top 20 krypto párů
cryptoSymbols = {'BTC/USD', 'ETH/USD', 'BNB/USD', 'SOL/USD', 'XRP/USD', ...
                 'DOGE/USD', 'ADA/USD', 'AVAX/USD', 'DOT/USD', 'TRX/USD', ...
                 'LINK/USD', 'MATIC/USD', 'LTC/USD', 'BCH/USD', 'XLM/USD', ...
                 'ATOM/USD', 'ETC/USD', 'HBAR/USD', 'NEAR/USD', 'ICP/USD'};

% ✅ OPRAVENÁ map() definice s cell array hodnotami
tfMap = containers.Map( ...
    {'1m', '5m', '15m', '1h', '1d'}, ...
    {minutes(1), minutes(5), minutes(15), hours(1), days(1)} ...
);

%% === Ověření přihlášení ===
fprintf('\n🔐 Ověřuji přihlašovací údaje k Money.Net...\n');
try
    c = moneynet(username, password);
    test = timeseries(c, 'BTC/USD', endDate - days(1), endDate, '1D');

    if isempty(test)
        error('Přihlášení OK, ale nepřišla žádná data.');
    else
        fprintf('✅ Přihlášení bylo úspěšné – spojení s Money.Net navázáno.\n');
    end
    close(c);  % zavřeme, znovu vytvoříme v každém vlákně
catch e
    error('❌ Autentizace selhala: %s\nZkontroluj uživatelské jméno a heslo.', e.message);
end

%% === Spuštění paralelního poolu ===
if isempty(gcp('nocreate'))
    parpool('local');
end

%% === Paralelní zpracování všech symbolů ===
parfor i = 1:length(cryptoSymbols)
    symbol = cryptoSymbols{i};
    fprintf('\n🔄 [%d/%d] Zpracovávám: %s\n', i, length(cryptoSymbols), symbol);

    try
        c_local = moneynet(username, password);  % nové připojení pro každý worker
        allData = table();

        % Rozdělení na časové bloky
        ranges = startDate:chunkSize:endDate;
        if ranges(end) < endDate
            ranges = [ranges, endDate];
        end

        for r = 1:(length(ranges)-1)
            fprintf('   ⏳ Chunk %s - %s\n', datestr(ranges(r)), datestr(ranges(r+1)));
            try
                d = timeseries(c_local, symbol, [ranges(r), ranges(r+1)], interval);
                allData = [allData; d];
            catch eChunk
                warning('   ⚠️ Chyba chunk %s-%s: %s', ...
                        datestr(ranges(r)), datestr(ranges(r+1)), eChunk.message);
            end
        end

        if isempty(allData)
            warning('❌ %s: Žádná data stažena.', symbol);
            continue;
        end

        allData.Timestamp = datetime(allData.Timestamp);
        tt = table2timetable(allData);
        baseName = strrep(symbol, '/', '_');

        tfKeys = tfMap.keys;
        for j = 1:length(tfKeys)
            tf = tfKeys{j};
            step = tfMap(tf);

            % Agregace
            if strcmp(tf, '1m')
                ttAgg = tt;
            else
                ttAgg = retime(tt, 'regular', @(x) nan, 'TimeStep', step);
                ttAgg.Open   = retime(tt(:, "Open"),   'regular', @(x)x(1), 'TimeStep', step).Open;
                ttAgg.High   = retime(tt(:, "High"),   'regular', @max,     'TimeStep', step).High;
                ttAgg.Low    = retime(tt(:, "Low"),    'regular', @min,     'TimeStep', step).Low;
                ttAgg.Close  = retime(tt(:, "Close"),  'regular', @(x)x(end),'TimeStep', step).Close;
                ttAgg.Volume = retime(tt(:, "Volume"), 'regular', @sum,     'TimeStep', step).Volume;
            end

            % === Uložení .parquet ===
            parquetPath = fullfile(outDirParquet, sprintf('%s_%s.parquet', baseName, tf));
            tParq = timetable2table(ttAgg);
            writetable(tParq, parquetPath, 'FileType', 'parquet');
            fprintf('   💾 Parquet: %s\n', parquetPath);

            % === Uložení .mat ===
            matPath = fullfile(outDirMat, sprintf('%s_%s.mat', baseName, tf));
            dataStruct = struct();
            dataStruct.(sprintf('%s_%s', baseName, tf)) = ttAgg;
            save(matPath, '-struct', 'dataStruct');
            fprintf('   💾 MAT:     %s\n', matPath);
        end

        close(c_local);
    catch ME
        warning('❌ [%s] Nezpracováno: %s', symbol, ME.message);
    end
end