# Binance Crypto Data Downloader

Tento skript nahrazuje `money_net_data.m` a používá MATLAB-Binance-API pro stahování krypto dat.

## Soubory

### <PERSON><PERSON><PERSON><PERSON> skripty
- `binance_crypto_data.m` - <PERSON><PERSON><PERSON><PERSON> skript pro stahování dat
- `test_binance_api.m` - Kompletní test všech funk<PERSON>í
- `quick_binance_test.m` - Rychlý test základních funkcí

### P<PERSON><PERSON><PERSON><PERSON> soubory (pro referenci)
- `money_net_data.m` - Původní skript s Money.Net
- `diagnose_moneynet.m` - Diagnostika Money.Net problémů

## Požadavky

1. **MATLAB R2019b nebo novější**
2. **MATLAB-Binance-API** - nainstalováno a přidáno do path
3. **Internetové připojení**

## Použití

### 1. <PERSON><PERSON><PERSON><PERSON> test
```matlab
run('quick_binance_test.m')
```

### 2. Kompletní test
```matlab
run('test_binance_api.m')
```

### 3. <PERSON><PERSON><PERSON><PERSON><PERSON> dat
```matlab
run('binance_crypto_data.m')
```

## Konfigurace

V `binance_crypto_data.m` můžete upravit:

```matlab
% Výstupní složka
baseDir = 'E:\02_Data\Binance_data';

% Časový rozsah
startDate = datetime('2024-01-01');
endDate = datetime('now');

% Symboly (Binance formát)
cryptoSymbols = {'BTCUSDT', 'ETHUSDT', 'BNBUSDT', ...};

% Intervaly
intervals = {'1m', '5m', '15m', '1h', '1d'};
```

## Výstupy

Skript vytvoří:
- **Parquet soubory**: `E:\02_Data\Binance_data\parquet\`
- **MAT soubory**: `E:\02_Data\Binance_data\mat\`

Formát názvů: `{SYMBOL}_{INTERVAL}.{ext}`
Příklad: `BTCUSDT_1h.parquet`, `ETHUSDT_1d.mat`

## Rozdíly oproti Money.Net

| Aspekt | Money.Net | Binance API |
|--------|-----------|-------------|
| **Symboly** | BTC/USD | BTCUSDT |
| **API klíče** | Vyžaduje | Nepotřeba (veřejné) |
| **Rate limiting** | Neznámé | 1200 req/min |
| **Dostupnost** | Problémy | Stabilní |
| **Data kvalita** | Agregovaná | Přímá z burzy |

## Rate Limiting

Skript implementuje pauzy:
- 0.1s mezi chunky dat
- 0.2s mezi intervaly
- 0.5s mezi symboly

## Troubleshooting

### Chyba: "Undefined function 'pub.getServerTime'"
- MATLAB-Binance-API není v path
- Spusťte: `addpath(genpath('cesta/k/MATLAB-Binance-API'))`

### Chyba: "Connection timeout"
- Zkontrolujte internetové připojení
- Možný firewall blokuje Binance API

### Chyba: "Rate limit exceeded"
- Zvyšte pauzy v skriptu
- Snižte počet současných požadavků

## Výhody Binance API

✅ **Zdarma** - veřejné endpointy bez API klíčů  
✅ **Spolehlivé** - stabilní infrastruktura  
✅ **Rychlé** - nízká latence  
✅ **Kompletní** - všechny hlavní krypto páry  
✅ **Aktuální** - real-time data  

## Kontakt

Pro problémy s MATLAB-Binance-API:
- GitHub: https://github.com/hughestu/MATLAB-Binance-API
- MATLAB File Exchange: https://uk.mathworks.com/matlabcentral/fileexchange/95558-matlab-binance-api
