%% === Stažení dat pro jeden symbol (rychlý test) ===
%% Použijte pro testování před spuštěním hlavního skriptu

% Konfigurace
symbol = 'BTCUSDT';  % Změňte podle potřeby
interval = '1h';     % 1m, 5m, 15m, 1h, 1d
days_back = 7;       % Kolik dní zpět

% Výstupní složka
outputDir = 'test_output';
if ~exist(outputDir, 'dir'), mkdir(outputDir); end

fprintf('🚀 Stahování dat pro %s (%s, %d dní zpět)...\n', symbol, interval, days_back);

try
    % Stažení dat (použijeme nejjednodušší syntaxi)
    fprintf('📊 Stahování klines...\n');
    T = pub.klines(symbol, interval);

    if isempty(T)
        error('Žádná data nebyla vrácena');
    end

    % pub.klines už vrací timetable, tak<PERSON>e jen převedeme na table pro kompatibilitu
    data = timetable2table(T);

    % Přejmenujeme sloupce pro konzistenci
    if ismember('Time', data.Properties.VariableNames)
        data.Properties.VariableNames{'Time'} = 'Timestamp';
    end
    
    fprintf('✅ Staženo %d řádků dat\n', height(data));
    fprintf('📅 Časový rozsah: %s až %s\n', ...
            char(min(data.Timestamp)), char(max(data.Timestamp)));
    
    % Ukázka dat
    fprintf('\n📋 Prvních 5 řádků:\n');
    disp(head(data, 5));
    
    fprintf('\n📋 Posledních 5 řádků:\n');
    disp(tail(data, 5));
    
    % Uložení
    filename = sprintf('%s_%s_%ddays', symbol, interval, days_back);
    
    % Parquet (MATLAB 2025a syntaxe)
    try
        parquetPath = fullfile(outputDir, [filename '.parquet']);
        parquetwrite(parquetPath, data);
        fprintf('\n💾 Uloženo jako Parquet: %s\n', parquetPath);
    catch parquetErr
        fprintf('\n⚠️  Parquet chyba: %s\n', parquetErr.message);
        fprintf('    Zkouším starší syntaxi...\n');
        try
            writetable(data, parquetPath, 'FileType', 'parquet');
            fprintf('💾 Uloženo jako Parquet (starší syntaxe): %s\n', parquetPath);
        catch
            fprintf('❌ Parquet není podporován\n');
        end
    end
    
    % MAT
    matPath = fullfile(outputDir, [filename '.mat']);
    tt = table2timetable(data);
    save(matPath, 'tt', 'data');
    fprintf('💾 Uloženo jako MAT: %s\n', matPath);
    
    % CSV pro kontrolu
    csvPath = fullfile(outputDir, [filename '.csv']);
    writetable(data, csvPath);
    fprintf('💾 Uloženo jako CSV: %s\n', csvPath);
    
    fprintf('\n🎉 Test úspěšný! Můžete spustit hlavní skript binance_crypto_data.m\n');
    
catch err
    fprintf('❌ Chyba: %s\n', err.message);
    fprintf('\n🔧 Možné příčiny:\n');
    fprintf('- MATLAB-Binance-API není správně nainstalováno\n');
    fprintf('- Symbol %s neexistuje na Binance\n', symbol);
    fprintf('- Problém s internetovým připojením\n');
    fprintf('- Rate limit překročen\n');
end
