%% === Stahování krypto dat pomocí MATLAB-Binance-API ===
%% Nahrazuje money_net_data.m s použitím Binance API

% Výstupní složky
baseDir = 'E:\02_Data\Binance_data';
outDirParquet = fullfile(baseDir, 'parquet');
outDirMat = fullfile(baseDir, 'mat');

if ~exist(outDirParquet, 'dir'), mkdir(outDirParquet); end
if ~exist(outDirMat, 'dir'), mkdir(outDirMat); end

% Rozsah a interval dat
startDate = datetime('2018-01-01');
endDate = datetime('now');
chunkSizeDays = 30;  % Binance má limity, stahujeme po 30 dnech

% Seznam Top 20 krypto párů (Binance formát)
cryptoSymbols = {'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT', 'XRPUSDT', ...
                 'DOGEUSDT', 'ADAUSDT', 'AVAXUSDT', 'DOTUSDT', 'TRXUSDT', ...
                 'LINKUSDT', 'MATICUSDT', 'LTCUSDT', 'BCHUSDT', 'XLMUSDT', ...
                 'ATOMUSDT', 'ETCUSDT', 'HBARUSDT', 'NEARUSDT', 'ICPUSDT'};

% Mapování intervalů (Binance formát)
intervalMap = containers.Map( ...
    {'1m', '5m', '15m', '1h', '1d'}, ...
    {'1m', '5m', '15m', '1h', '1d'} ...
);

% Mapování pro agregaci
tfMap = containers.Map( ...
    {'1m', '5m', '15m', '1h', '1d'}, ...
    {minutes(1), minutes(5), minutes(15), hours(1), days(1)} ...
);

fprintf('🚀 Spouštím stahování krypto dat z Binance API...\n\n');

%% === Test Binance API připojení ===
fprintf('🔍 Testování Binance API připojení...\n');
try
    % Test veřejného endpointu (nepotřebuje API klíče)
    serverTime = pub.getServerTime();
    fprintf('✅ Binance API je dostupné\n');
    if serverTime > 0
        serverDateTime = datetime(serverTime/1000, 'ConvertFrom', 'posixtime');
        fprintf('   Server čas: %s\n', char(serverDateTime));
    else
        fprintf('   Server čas: OK (timestamp: %d)\n', serverTime);
    end
    
    % Test získání informací o symbolech
    exchangeInfo = pub.exchangeInfo();
    fprintf('✅ Exchange info získáno - %d symbolů dostupných\n', length(exchangeInfo.symbols));
    
catch err
    error('❌ Binance API není dostupné: %s', err.message);
end

%% === Funkce pro stahování historických dat ===
function data = getBinanceKlines(symbol, interval, startTime, endTime)
    try
        % Konverze datetime na timestamp (milisekundy)
        startTs = int64(posixtime(startTime) * 1000);
        endTs = int64(posixtime(endTime) * 1000);
        
        % Stažení dat pomocí pub.klines
        klines = pub.klines(symbol, interval, 'startTime', startTs, 'endTime', endTs, 'limit', 1000);
        
        if isempty(klines)
            data = table();
            return;
        end
        
        % Konverze na tabulku
        timestamps = datetime(klines(:,1)/1000, 'ConvertFrom', 'posixtime');
        opens = klines(:,2);
        highs = klines(:,3);
        lows = klines(:,4);
        closes = klines(:,5);
        volumes = klines(:,6);
        
        data = table(timestamps, opens, highs, lows, closes, volumes, ...
                    'VariableNames', {'Timestamp', 'Open', 'High', 'Low', 'Close', 'Volume'});
        
        fprintf('   ✅ Staženo %d řádků pro %s (%s)\n', height(data), symbol, interval);
        
    catch err
        fprintf('   ❌ Chyba při stahování %s: %s\n', symbol, err.message);
        data = table();
    end
end

%% === Stahování dat pro všechny symboly ===
totalSymbols = length(cryptoSymbols);
intervals = {'1m', '5m', '15m', '1h', '1d'};

for i = 1:totalSymbols
    symbol = cryptoSymbols{i};
    fprintf('\n🔄 [%d/%d] Zpracovávám: %s\n', i, totalSymbols, symbol);
    
    try
        % Pro každý interval
        for j = 1:length(intervals)
            interval = intervals{j};
            fprintf('  📊 Interval: %s\n', interval);
            
            allData = table();
            
            % Rozdělení na časové bloky (Binance má limity)
            currentStart = startDate;
            
            while currentStart < endDate
                currentEnd = min(currentStart + days(chunkSizeDays), endDate);
                
                fprintf('    ⏳ Chunk %s - %s\n', ...
                        datestr(currentStart, 'yyyy-mm-dd'), ...
                        datestr(currentEnd, 'yyyy-mm-dd'));
                
                try
                    chunkData = getBinanceKlines(symbol, interval, currentStart, currentEnd);
                    if ~isempty(chunkData)
                        allData = [allData; chunkData];
                    end
                    
                    % Krátká pauza mezi požadavky (rate limiting)
                    pause(0.1);
                    
                catch eChunk
                    warning('    ⚠️ Chyba chunk %s-%s: %s', ...
                            datestr(currentStart, 'yyyy-mm-dd'), ...
                            datestr(currentEnd, 'yyyy-mm-dd'), ...
                            eChunk.message);
                end
                
                currentStart = currentEnd;
            end
            
            if isempty(allData)
                warning('❌ %s (%s): Žádná data stažena.', symbol, interval);
                continue;
            end
            
            % Odstranění duplicitních záznamů
            [~, uniqueIdx] = unique(allData.Timestamp);
            allData = allData(uniqueIdx, :);
            allData = sortrows(allData, 'Timestamp');
            
            % Konverze na timetable
            tt = table2timetable(allData);
            baseName = symbol;
            
            % === Uložení .parquet ===
            parquetPath = fullfile(outDirParquet, sprintf('%s_%s.parquet', baseName, interval));
            tParq = timetable2table(tt);
            writetable(tParq, parquetPath, 'FileType', 'parquet');
            fprintf('    💾 Parquet: %s\n', parquetPath);
            
            % === Uložení .mat ===
            matPath = fullfile(outDirMat, sprintf('%s_%s.mat', baseName, interval));
            dataStruct = struct();
            dataStruct.(sprintf('%s_%s', baseName, interval)) = tt;
            save(matPath, '-struct', 'dataStruct');
            fprintf('    💾 MAT: %s\n', matPath);
            
            % Krátká pauza mezi intervaly
            pause(0.2);
        end
        
        % Pauza mezi symboly
        pause(0.5);
        
    catch ME
        warning('❌ [%s] Nezpracováno: %s', symbol, ME.message);
    end
end

fprintf('\n✅ Stahování z Binance API dokončeno!\n');
fprintf('📁 Data uložena v: %s\n', baseDir);
fprintf('\n📊 Statistiky:\n');
fprintf('- Symboly: %d\n', totalSymbols);
fprintf('- Intervaly: %s\n', strjoin(intervals, ', '));
fprintf('- Časový rozsah: %s až %s\n', datestr(startDate), datestr(endDate));
fprintf('\n💡 Poznámky:\n');
fprintf('- Použito Binance API (zdarma, veřejné endpointy)\n');
fprintf('- Data jsou skutečná OHLCV data z Binance\n');
fprintf('- Rate limiting: 0.1s mezi chunky, 0.2s mezi intervaly, 0.5s mezi symboly\n');
fprintf('- Duplicitní záznamy byly odstraněny\n');
